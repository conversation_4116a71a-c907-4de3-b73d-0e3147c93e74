import { requireProject } from '$lib/server/auth';
import { requireUser } from '$lib/server/auth';
import { redirect } from 'sveltekit-flash-message/server';
import type { PageServerLoad } from './$types';

export const load = (async ({ params, locals, cookies, depends }) => {
	depends('project:budget');

	await requireUser();

	const { supabase } = locals;
	const { org_name, client_name, project_name } = requireProject();

	// Fetch the project data
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select(
			`*,
				client!inner(name, client_id, organization(name, org_id)),
				wbs_library:wbs_library(wbs_library_item(*)),
				budget_line_item_current(*)`,
		)
		.eq('client.organization.name', org_name)
		.eq('client.name', client_name)
		.eq('name', project_name)
		.limit(1)
		.maybeSingle();

	if (projectError || !projectData) {
		console.error('Error fetching project:', projectError);
		return redirect(
			`/org/${encodeURIComponent(params.org_name)}/clients/${encodeURIComponent(client_name)}`,
			{ type: 'error', message: 'Project not found' },
			cookies,
		);
	}

	// Extract related data returned from the join
	const allWbsItems = projectData.wbs_library?.wbs_library_item || [];
	const rawCurrentItems =
		projectData.budget_line_item_current.map((bli) => {
			return {
				...bli,
				label: 'current',
			};
		}) || [];

	// Check user permissions
	const { data: canEditProject } = await supabase.rpc('can_modify_project', {
		project_id_param: projectData.project_id,
	});

	const wbsItems =
		allWbsItems?.map((i) => ({
			label: `${i.code}: ${i.description}`,
			value: i.wbs_library_item_id,
		})) || [];

	return {
		client: projectData.client,
		wbsItems,
		allWbsItems: allWbsItems || [],
		rawCurrentItems: rawCurrentItems || [],
		canEditProject: canEditProject || false,
	};
}) satisfies PageServerLoad;
