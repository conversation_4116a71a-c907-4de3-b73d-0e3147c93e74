<script lang="ts">
	import type { PageProps } from './$types';
	import { createBudgetDataForDepth, createCurrentBudgetHierarchy } from '$lib/budget_utils';
	import PieChart from '$lib/components/ui/chart/chart-pie-legend.svelte';
	// import PieChartTest from '$lib/components/ui/chart/chart-pie-test.svelte';
	import { scaleSequential } from 'd3-scale';
	import { interpolateGreens } from 'd3-scale-chromatic';
	import Sunburst from '$lib/components/ui/chart/chart-sunburst.svelte';

	let { data }: PageProps = $props();

	const depth = $state(3);

	const stackedData = $derived(
		createBudgetDataForDepth(data.allWbsItems, data.rawCurrentItems, depth, 'current')
			.filter((d) => d.totalValue !== 0)
			.map((d) => ({
				...d,
				totalValue: Math.round(d.totalValue),
			}))
			.sort((a, b) => b.totalValue - a.totalValue),
	);
	const colorScale = $derived(
		scaleSequential()
			.domain([0, stackedData.length - 1])
			.interpolator(interpolateGreens),
	);

	const chartConfig = $derived(
		stackedData
			.map((d, i) => ({
				[d.wbsCode]: { label: d.wbsDescription, color: colorScale(stackedData.length - i - 1) },
			}))
			.reduce((acc, curr) => ({ ...acc, ...curr }), {}),
	);

	const hierarchicalData = $derived(
		createCurrentBudgetHierarchy(data.allWbsItems, data.rawCurrentItems),
	);
</script>

<div class="container">
	<h1 class="sr-only text-3xl font-semibold">Overview</h1>
	<div class="my-16 h-[800px] w-full">
		<Sunburst data={hierarchicalData} />
	</div>

	<div class="h-[400px] resize overflow-auto rounded-sm border p-4">
		<PieChart
			chartData={stackedData}
			key="wbsCode"
			value="totalValue"
			title="Current Budget"
			description="Budget by WBS Code"
			{chartConfig}
		/>
	</div>

	<!-- <div class="h-[400px] resize overflow-auto rounded-sm border p-4">
		<PieChartTest />
	</div> -->

	<div class="mt-8">
		<pre>
			{JSON.stringify(stackedData, null, 2)}
		</pre>
	</div>
</div>
