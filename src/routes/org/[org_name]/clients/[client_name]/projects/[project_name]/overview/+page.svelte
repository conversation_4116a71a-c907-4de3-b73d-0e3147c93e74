<script lang="ts">
	import type { PageProps } from './$types';
	import { createBudgetDataForDepth, createCurrentBudgetHierarchy } from '$lib/budget_utils';

	let { data }: PageProps = $props();

	const depth = $state(3);

	const stackedData = $derived(
		createBudgetDataForDepth(data.allWbsItems, data.rawCurrentItems, depth, 'current')
			.filter((d) => d.totalValue !== 0)
			.map((d) => ({
				...d,
				totalValue: Math.round(d.totalValue),
			}))
			.sort((a, b) => b.totalValue - a.totalValue),
	);

	const hierarchicalData = $derived(
		createCurrentBudgetHierarchy(data.allWbsItems, data.rawCurrentItems),
	);
</script>

<div class="container">
	<h1 class="sr-only text-3xl font-semibold">Overview</h1>

	<div class="mt-8">
		<pre>{JSON.stringify(stackedData, null, 2)}</pre>
	</div>
</div>
